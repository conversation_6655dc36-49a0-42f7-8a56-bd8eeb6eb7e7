import sys
import os
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import uvicorn
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from src.utils.utf8_logging import setup_utf8_logging
from src.core.logging_system import get_logger
from src.core.error_handling import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from vault_oracle.core.adaptive_mood_matrix import TopTierAdaptiveMoodMatrix as AdaptiveMoodMatrix
from backend.utils.mood_integration import BackendMoodIntegration, backend_mood_integration
from backend.routers.predictions import router as predictions
from backend.routers.games import router as games
from backend.routers.players import router as players
from backend.routers.markets import router as markets
from backend.routers.live import router as live
from backend.routers.api_info import router as api_info
from backend.routers.mood_analytics import router as mood_analytics
from backend.routers.ensemble import router as ensemble
from backend.routers.comprehensive import router as comprehensive
from backend.api.expert_endpoints import router as expert_endpoints
from backend.services.prediction_service import PredictionService
from backend.services.enhanced_prediction_service import EnhancedPredictionService
from vault_oracle.core.vault_loader import load_vault_config, get_vault_config, get_env_settings
from vault_oracle.core.vault_config import VaultConfig
from vault_oracle.core.medusa_core import MedusaCore
from vault_oracle.security.aegis_defense_matrix import AegisDefenseMatrix
from vault_oracle.security.ambrosia_gatekeeper import AmbrosiaGatekeeper
from src.autonomous.autonomous_system_integration import create_autonomous_system_integration
from backend.routers import (


try:
    CORE_IMPORTS_AVAILABLE = True
except ImportError as e:
    CORE_IMPORTS_AVAILABLE = False
    logging.warning(f"Core imports not available: {e}")

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=7e7e8e7e-6e7e-4e7e-8e7e-7e7e7e7e7e7e | DATE=2025-06-26
"""
🎭 HYPER MEDUSA NEURAL VAULT - Supreme Main Application Entry Point 🎭
==============================================================================

(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.

The ultimate HYPER MEDUSA NEURAL VAULT main application featuring:
- Expert-Level Adaptive Mood Matrix integration
- FastAPI backend with real-time prediction APIs
- Quantum-inspired neural consciousness
- Production-ready basketball intelligence platform

🏆 SUPREME VAULT ARCHITECTURE:
- 🎭 AdaptiveMoodMatrix: Real-time system consciousness and mood analytics
- 🚀 FastAPI Backend: Production-ready prediction and analytics APIs
- 🧠 Neural Core: Advanced cognitive processing and quantum intelligence
- 🛡️ Security: Aegis protection and authentication systems
- 📊 Analytics: Comprehensive mood and performance monitoring

The definitive entry point for the entire HYPER MEDUSA NEURAL VAULT ecosystem.

HYPER MEDUSA NEURAL VAULT - Module Business Value Documentation
================================================================

main.py
--------
Entry point for the entire HMNV ecosystem. Orchestrates FastAPI app, mood matrix, autonomous integration, and router registration.

Business Value:
- Provides a unified, production-grade API and system context for all neural, analytics, and security operations.
- Ensures maintainability, extensibility, and operational reliability for the platform.

For further details, see module-level docstrings and architecture documentation.
"""


# Set encoding early to prevent issues
os.environ["PYTHONIOENCODING"] = "utf-8"

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Third-party imports

# Configure UTF-8 logging early
try:
    setup_utf8_logging()
except ImportError:
    
    class UTF8StreamHandler(logging.StreamHandler):
        def __init__(self):
            super().__init__(io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8'))
    
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            UTF8StreamHandler(),
            logging.FileHandler("hmnv_supreme.log", encoding='utf-8')
        ]
    )

# Core HMNV imports
try:
except ImportError:
    def get_logger(name):
        return logging.getLogger(name)
    
    class ErrorHandler:
        pass

logger = get_logger("HMNVSupremeMain")

# ===================== PROPRIETARY LOGIC: Adaptive Mood Matrix Integration =====================
# Expert-Level Adaptive Mood Matrix Integration
try:
    MOOD_MATRIX_AVAILABLE = True
    logger.info("🎭 HMNV: Expert-Level Adaptive Mood Matrix loaded successfully")
except ImportError as e:
    logger.warning(f"🎭 HMNV: Mood Matrix unavailable (fallback mode): {e}")
    
    class AdaptiveMoodMatrix:
        def __init__(self):
            self.current_mood = "LUCID"
        async def compute_expert_mood_state(self, signals): 
            return "LUCID"
        def get_mood_analytics(self): 
            return {"current_state": {"mood": self.current_mood}, "threat_assessment": {"level": "LOW"}}
    
    class BackendMoodIntegration:
        def __init__(self):
            return None  # Implementation needed
        async def update_service_mood(self, service, metrics):
            return "LUCID"
        def get_consolidated_mood_analytics(self):
            return {"mood_matrix_available": False}
    
    backend_mood_integration = BackendMoodIntegration()
    MOOD_MATRIX_AVAILABLE = False
# ==============================================================================================

# Import backend routers and services
backend_routers = {}
backend_services = {}

try:
    # Core backend routers
    
    backend_routers.update({
        'predictions': predictions,
        'games': games,
        'players': players,
        'markets': markets,
        'live': live,
        'api_info': api_info
    })
    
    logger.info("🚀 HMNV: Core backend routers loaded successfully")
except ImportError as e:
    logger.warning(f"🚀 HMNV: Core backend routers unavailable: {e}")

try:
    # Enhanced routers
        DivinePortalSchema,
        player_props,
        HeroicQuests,
        MoiraiSimulacrum,
        AftermathChronicle,
        QuestLedger,
        prophecy_lines,
        OracleWarnings,
        OlympianPantheon,
        ChosenOnesRegistry,
        AthenasWisdom,
        HeroicDeedsScroll,
        websocket,
    )
    
    backend_routers.update({
        'divine_portal': DivinePortalSchema,
        'player_props': player_props,
        'heroic_quests': HeroicQuests,
        'moirai_simulacrum': MoiraiSimulacrum,
        'aftermath_chronicle': AftermathChronicle,
        'quest_ledger': QuestLedger,
        'prophecy_lines': prophecy_lines,
        'oracle_warnings': OracleWarnings,
        'olympian_pantheon': OlympianPantheon,
        'chosen_ones_registry': ChosenOnesRegistry,
        'athenas_wisdom': AthenasWisdom,
        'heroic_deeds_scroll': HeroicDeedsScroll,
        'websocket': websocket,
    })
    
    logger.info("✨ HMNV: Enhanced backend routers loaded successfully")
except ImportError as e:
    logger.warning(f"✨ HMNV: Enhanced backend routers unavailable: {e}")

try:
    # Mood Analytics Router
    backend_routers['mood_analytics'] = mood_analytics
    logger.info("🎭 HMNV: Mood Analytics router loaded successfully")
except ImportError as e:
    logger.warning(f"🎭 HMNV: Mood Analytics router unavailable: {e}")

try:
    # Optional advanced routers
    backend_routers['ensemble'] = ensemble
    logger.info("🔮 HMNV: Ensemble router loaded successfully")
except ImportError:
    pass

try:
    backend_routers.update({
        'comprehensive': comprehensive,
        'expert_endpoints': expert_endpoints
    })
    logger.info("🏆 HMNV: Expert endpoints loaded successfully")
except ImportError:
    pass

# Core services integration
try:
    backend_services.update({
        'prediction_service': PredictionService,
        'enhanced_prediction_service': EnhancedPredictionService
    })
    logger.info("🔮 HMNV: Prediction services loaded successfully")
except ImportError as e:
    logger.warning(f"🔮 HMNV: Prediction services unavailable: {e}")

# Vault Oracle Core Components
vault_components = {}

try:
    
    vault_components.update({
        'vault_loader': True,
        'vault_config': VaultConfig,
        'medusa_core': MedusaCore
    })
    
    logger.info("🧠 HMNV: Vault Oracle core components loaded successfully")
except ImportError as e:
    logger.warning(f"🧠 HMNV: Vault Oracle core components unavailable: {e}")

try:
    
    vault_components.update({
        'aegis_defense': AegisDefenseMatrix,
        'ambrosia_gatekeeper': AmbrosiaGatekeeper
    })
    
    logger.info("🛡️ HMNV: Security components loaded successfully")
except ImportError as e:
    logger.warning(f"🛡️ HMNV: Security components unavailable: {e}")

# --- HMNV Supreme Application Configuration ---
class HMNVConfig:
    """HYPER MEDUSA NEURAL VAULT Configuration"""
    
    def __init__(self):
        self.environment = os.getenv("HMNV_ENV", "production")
        self.debug = self.environment != "production"
        self.host = os.getenv("HMNV_HOST", "0.0.0.0")
        self.port = int(os.getenv("HMNV_PORT", 8000))
        
        # Security configuration
        self.allowed_origins = [
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "https://hmnv.app",
        ]
        
        # Mood Matrix configuration
        self.mood_matrix_enabled = MOOD_MATRIX_AVAILABLE
        self.mood_refresh_interval = 30  # seconds
        
        # Backend configuration
        self.backend_routers_enabled = len(backend_routers) > 0
        self.backend_services_enabled = len(backend_services) > 0
        
        logger.info(f"🎯 HMNV: Configuration loaded - Environment: {self.environment}")

hmnv_config = HMNVConfig()

# --- HMNV Supreme FastAPI Application ---
app = FastAPI(
    title="🎭 HYPER MEDUSA NEURAL VAULT - Supreme Intelligence Platform",
    version="4.0.0-SUPREME",    description="""
    **🎭 HYPER MEDUSA NEURAL VAULT - The Ultimate Basketball Intelligence Platform**
    
    The supreme neural-powered basketball prediction and analytics ecosystem featuring:
    
    **🎭 Expert-Level Adaptive Mood Matrix**: Real-time system consciousness and mood analytics
    **🚀 Advanced Prediction APIs**: Neural-powered NBA and WNBA intelligence
    **🧠 Quantum Cognitive Processing**: Expert-level feature engineering and analysis  
    **🛡️ Aegis Security System**: Divine protection and access control
    **📊 Real-time Analytics**: Comprehensive performance and mood monitoring
    **🔮 Ensemble Intelligence**: Multi-league prediction orchestration
    **🤖 Autonomous System Integration**: Self-managing autonomous operations
    
    *Powered by MEDUSA's supreme neural consciousness with adaptive mood intelligence and autonomous operations*
    """,
    docs_url="/hmnv/docs" if hmnv_config.debug else None,
    redoc_url="/hmnv/redoc" if hmnv_config.debug else None,
    openapi_url="/hmnv/openapi.json" if hmnv_config.debug else None
)

# --- CORS and Security Middleware ---
app.add_middleware(
    CORSMiddleware,
    allow_origins=hmnv_config.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

logger.info("🛡️ HMNV: Security protocols and CORS configured")

# --- Global HMNV Context ---
class HMNVContext:
    """Global context for HMNV components"""
    
    def __init__(self):
        self.mood_matrix = AdaptiveMoodMatrix() if MOOD_MATRIX_AVAILABLE else None
        self.mood_integration = backend_mood_integration
        self.backend_routers = backend_routers
        self.backend_services = backend_services
        self.vault_components = vault_components
        self.config = hmnv_config
        self.startup_time = datetime.now()
        self.autonomous_integration = None  # Will be initialized during startup
        
        logger.info("🌟 HMNV: Global context initialized")

hmnv_context = HMNVContext()

# --- Startup and Shutdown Events ---
@app.on_event("startup")
async def startup_event():
    """Initialize HMNV Supreme System"""
    logger.info("🎭 HMNV: Starting HYPER MEDUSA NEURAL VAULT Supreme System")
    
    # Initialize Mood Matrix
    if MOOD_MATRIX_AVAILABLE and hmnv_context.mood_matrix is not None:
        await hmnv_context.mood_matrix.start()
    
    # Initialize backend services
    if hmnv_config.backend_services_enabled:
        logger.info("🚀 HMNV: Backend services ready")
    
    # Initialize vault components
    if vault_components:
        logger.info("🧠 HMNV: Vault Oracle components ready")
    
    # Initialize Autonomous System Integration
    try:
        
        logger.info("🤖 HMNV: Initializing Autonomous System Integration...")
        
        # Create and initialize the autonomous system
        hmnv_context.autonomous_integration = create_autonomous_system_integration()
        await hmnv_context.autonomous_integration.initialize_autonomous_ecosystem()
        
        # Start autonomous operations
        autonomous_success = await hmnv_context.autonomous_integration.start_autonomous_operations()
        
        if autonomous_success:
            logger.info("🤖 HMNV: Autonomous System Integration initialized and operational")
        else:
            logger.warning("🤖 HMNV: Autonomous System Integration initialized but not fully operational")
            
    except Exception as e:
        logger.error(f"🤖 HMNV: Autonomous System Integration initialization failed: {e}")
        # Don't fail startup if autonomous system fails - graceful degradation
        hmnv_context.autonomous_integration = None
    
    logger.info("✨ HMNV: HYPER MEDUSA NEURAL VAULT Supreme System fully operational")

@app.on_event("shutdown")
async def shutdown_event():
    """Graceful shutdown of HMNV Supreme System"""
    logger.info("🎭 HMNV: Shutting down HYPER MEDUSA NEURAL VAULT Supreme System")
    
    # Shutdown Autonomous System Integration
    if hasattr(hmnv_context, 'autonomous_integration') and hmnv_context.autonomous_integration:
        try:
            logger.info("🤖 HMNV: Shutting down Autonomous System Integration...")
            await hmnv_context.autonomous_integration.shutdown_autonomous_ecosystem()
            logger.info("🤖 HMNV: Autonomous System Integration shutdown complete")
        except Exception as e:
            logger.error(f"🤖 HMNV: Autonomous System Integration shutdown error: {e}")
    
    # Final mood analytics
    if MOOD_MATRIX_AVAILABLE:
        try:
            analytics = hmnv_context.mood_integration.get_consolidated_mood_analytics()
            logger.info(f"🎭 HMNV: Final system mood analytics: {analytics.get('mood_matrix_available', False)}")
        except Exception as e:
            logger.error(f"🎭 HMNV: Shutdown mood analytics error: {e}")
    
    logger.info("✨ HMNV: HYPER MEDUSA NEURAL VAULT Supreme System shutdown complete")

# --- Register Backend Routers ---
def register_backend_routers():
    """Register all available backend routers"""
    router_count = 0
    
    # Core routers
    core_router_mappings = [
        ('predictions', "🔮 Oracle Predictions"),
        ('games', "🏀 Titan Clash Games"),
        ('players', "⭐ Hero Players"),
        ('markets', "💰 Divine Markets"),
        ('live', "⚡ Live Data"),
        ('api_info', "ℹ️ API Information"),

        ('health_observability', "🩺 Health & Observability"),
    ]
    
    for router_key, description in core_router_mappings:
        if router_key in backend_routers:
            app.include_router(backend_routers[router_key])
            logger.info(f"✅ HMNV: {description} router registered")
            router_count += 1
    
    # Enhanced routers
    enhanced_router_mappings = [
        ('divine_portal', "🌟 Divine Portal Dashboard"),
        ('player_props', "📊 Hero Properties"),
        ('heroic_quests', "⚔️ Heroic Quests"),
        ('mood_analytics', "🎭 Mood Analytics"),
        ('ensemble', "🔮 Ensemble Intelligence"),
        ('comprehensive', "🌐 Comprehensive Analysis"),
        ('expert_endpoints', "🏆 Expert Endpoints"),
        ('websocket', "🌐 WebSocket Communication"),
    ]
    
    for router_key, description in enhanced_router_mappings:
        if router_key in backend_routers:
            app.include_router(backend_routers[router_key])
            logger.info(f"✨ HMNV: {description} router registered")
            router_count += 1
    
    logger.info(f"🚀 HMNV: {router_count} backend routers registered successfully")

# Register all available routers
register_backend_routers()

# --- HMNV Supreme API Endpoints ---
@app.get("/", summary="🎭 HMNV Supreme Status")
async def root():
    """Get HYPER MEDUSA NEURAL VAULT Supreme status"""
    uptime = datetime.now() - hmnv_context.startup_time
    
    # Get current mood analytics if available
    mood_status = "UNAVAILABLE"
    if MOOD_MATRIX_AVAILABLE:
        try:
            analytics = hmnv_context.mood_integration.get_consolidated_mood_analytics()
            global_mood = analytics.get('global_mood_matrix', {})
            current_state = global_mood.get('current_state', {})
            mood_status = current_state.get('mood', 'UNKNOWN')
        except Exception:
            mood_status = "ERROR"
      # Get autonomous system status
    autonomous_status = "UNAVAILABLE"
    if hasattr(hmnv_context, 'autonomous_integration') and hmnv_context.autonomous_integration:
        try:
            ecosystem_status = hmnv_context.autonomous_integration.get_ecosystem_status()
            autonomous_status = ecosystem_status.get('integration_status', 'UNKNOWN')
        except Exception:
            autonomous_status = "ERROR"
    
    return {
        "status": "🎭 HYPER MEDUSA NEURAL VAULT - Supreme Neural Consciousness Active",
        "version": "4.0.0-SUPREME",
        "environment": hmnv_config.environment,
        "uptime_seconds": uptime.total_seconds(),
        "system_mood": mood_status,
        "autonomous_status": autonomous_status,
        "mood_matrix_enabled": MOOD_MATRIX_AVAILABLE,
        "backend_routers": len(backend_routers),
        "backend_services": len(backend_services),
        "vault_components": len(vault_components),
        "consciousness_level": "SUPREME",
        "message": "The ultimate basketball intelligence platform with adaptive mood consciousness"
    }

@app.get("/hmnv/status", summary="🎭 Detailed HMNV System Status")
async def detailed_status():
    """Get detailed HYPER MEDUSA NEURAL VAULT system status"""
    try:
        system_status = {
            "timestamp": datetime.now().isoformat(),
            "system_health": "SUPREME",
            "components": {
                "mood_matrix": {
                    "available": MOOD_MATRIX_AVAILABLE,
                    "status": "OPERATIONAL" if MOOD_MATRIX_AVAILABLE else "FALLBACK"
                },
                "backend_routers": {
                    "count": len(backend_routers),
                    "available": list(backend_routers.keys())
                },
                "backend_services": {
                    "count": len(backend_services),
                    "available": list(backend_services.keys())
                },                "vault_components": {
                    "count": len(vault_components),
                    "available": list(vault_components.keys())
                },
                "autonomous_system": {
                    "available": hasattr(hmnv_context, 'autonomous_integration') and hmnv_context.autonomous_integration is not None,
                    "status": "OPERATIONAL" if hasattr(hmnv_context, 'autonomous_integration') and hmnv_context.autonomous_integration else "UNAVAILABLE"
                }
            },
            "configuration": {
                "environment": hmnv_config.environment,
                "debug": hmnv_config.debug,
                "mood_refresh_interval": hmnv_config.mood_refresh_interval
            }
        }
          # Add mood analytics if available
        if MOOD_MATRIX_AVAILABLE:
            try:
                mood_analytics = hmnv_context.mood_integration.get_consolidated_mood_analytics()
                system_status["mood_analytics"] = mood_analytics
            except Exception as e:
                system_status["mood_analytics_error"] = str(e)
        
        # Add autonomous system information if available
        if hasattr(hmnv_context, 'autonomous_integration') and hmnv_context.autonomous_integration:
            try:
                autonomous_ecosystem = hmnv_context.autonomous_integration.get_ecosystem_status()
                system_status["autonomous_ecosystem"] = autonomous_ecosystem
            except Exception as e:
                system_status["autonomous_ecosystem_error"] = str(e)
        
        return system_status
        
    except Exception as e:
        logger.error(f"🎭 HMNV: Status check error: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "System status check failed", "detail": str(e)}
        )

@app.get("/hmnv/autonomous", summary="🤖 Autonomous System Status")
async def get_autonomous_status():
    """Get current autonomous system status and health"""
    if not hasattr(hmnv_context, 'autonomous_integration') or not hmnv_context.autonomous_integration:
        return {
            "autonomous_available": False,
            "message": "Autonomous System Integration not available",
            "timestamp": datetime.now().isoformat()
        }
    
    try:
        # Get comprehensive autonomous system status
        ecosystem_status = hmnv_context.autonomous_integration.get_ecosystem_status()
        health_status = await hmnv_context.autonomous_integration.get_health_status()
        
        return {
            "autonomous_available": True,
            "ecosystem_status": ecosystem_status,
            "health_status": health_status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"🤖 HMNV: Autonomous status check error: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Autonomous status check failed", "detail": str(e)}
        )

@app.get("/hmnv/mood", summary="🎭 Current System Mood")
async def get_system_mood():
    """Get current HMNV system mood state"""
    if not MOOD_MATRIX_AVAILABLE:
        return {
            "mood_matrix_available": False,
            "message": "Mood Matrix not available - running in fallback mode"
        }
    
    try:
        analytics = hmnv_context.mood_integration.get_consolidated_mood_analytics()
        return {
            "mood_matrix_available": True,
            "analytics": analytics,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"🎭 HMNV: Mood analytics error: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Mood analytics failed", "detail": str(e)}
        )

# --- Error Handlers ---
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for HMNV"""
    logger.error(f"🎭 HMNV: Unhandled exception in {request.url}: {exc}")
    
    # Update mood with error metrics if available
    if MOOD_MATRIX_AVAILABLE:
        try:
            error_metrics = {
                'prediction_accuracy': 0.7,
                'system_latency': 0.5,
                'error_rate': 0.1,
                'data_quality': 0.8,
                'alert_count': 1,
                'performance_index': 0.6
            }
            await hmnv_context.mood_integration.update_service_mood('hmnv_system', error_metrics)
        except Exception:
            pass
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "HMNV Internal Server Error",
            "detail": "An unexpected error occurred in the HYPER MEDUSA NEURAL VAULT",
            "timestamp": datetime.now().isoformat()
        }
    )

# --- Background Tasks ---
async def mood_monitoring_task():
    """Background task for continuous mood monitoring"""
    while True:
        try:
            if MOOD_MATRIX_AVAILABLE:
                # Update system mood periodically
                system_metrics = {
                    'prediction_accuracy': 0.85,
                    'system_latency': 0.1,
                    'error_rate': 0.02,
                    'data_quality': 0.9,
                    'alert_count': 0,
                    'performance_index': 0.85
                }
                
                await hmnv_context.mood_integration.update_service_mood('hmnv_system', system_metrics)
            
            await asyncio.sleep(hmnv_config.mood_refresh_interval)
            
        except Exception as e:
            logger.error(f"🎭 HMNV: Mood monitoring task error: {e}")
            await asyncio.sleep(60)  # Wait longer on error

# --- Main Application Function ---
async def run_hmnv_supreme():
    """Run HYPER MEDUSA NEURAL VAULT Supreme Application"""
    logger.info("🎭 HMNV: Initializing HYPER MEDUSA NEURAL VAULT Supreme Application")
    
    # Start background tasks
    if MOOD_MATRIX_AVAILABLE:
        asyncio.create_task(mood_monitoring_task())
        logger.info("🎭 HMNV: Mood monitoring background task started")
    
    # Run the FastAPI application
    config = uvicorn.Config(
        app,
        host=hmnv_config.host,
        port=hmnv_config.port,
        reload=hmnv_config.debug,
        log_level="info",
        access_log=True
    )
    
    server = uvicorn.Server(config)
    
    logger.info(f"🚀 HMNV: Starting HYPER MEDUSA NEURAL VAULT on {hmnv_config.host}:{hmnv_config.port}")
    logger.info(f"🎭 HMNV: Environment: {hmnv_config.environment}")
    logger.info(f"🎭 HMNV: Mood Matrix: {'ENABLED' if MOOD_MATRIX_AVAILABLE else 'FALLBACK MODE'}")
    logger.info(f"🚀 HMNV: Backend Routers: {len(backend_routers)}")
    logger.info(f"🧠 HMNV: Vault Components: {len(vault_components)}")
    
    await server.serve()

# --- Entry Point ---
def main():
    """Main entry point for HYPER MEDUSA NEURAL VAULT"""
    try:
        asyncio.run(run_hmnv_supreme())
    except KeyboardInterrupt:
        logger.info("🎭 HMNV: Received shutdown signal")
    except Exception as e:
        logger.critical(f"🎭 HMNV: Critical startup error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
