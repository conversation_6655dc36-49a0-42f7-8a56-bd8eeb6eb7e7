#!/usr/bin/env python3
"""
NBA API Parameter Manager - MEDUSA Integration
===================================================

Comprehensive NBA API parameter management system that integrates the provided
parameter classes into the HYPER_MEDUSA_NEURAL_VAULT ecosystem for systematic
10-year NBA/WNBA data collection.

Features:
- Complete parameter class integration
- Systematic endpoint parameter management
- Oracle Memory optimized data collection
- Basketball-aware parameter selection
- 10-year historical data coverage
- Real-time parameter validation
- Advanced error handling and recovery

Architecture:
NBA API Parameters → Oracle Memory → MEDUSA Neural Processing
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import json
import sys

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import Oracle Memory and existing components
try:
    from vault_oracle.core.OracleMemory import (
        ExpertOracleMemory,
        ExpertQuantumMemoryEntry,
        MemoryPriority,
        GamePhase,
        MemoryDimension
    )
except ImportError as e:
    logging.warning(f"Oracle Memory import failed: {e}. Using fallback mode.")
    # Create fallback classes
    class ExpertOracleMemory:
        def __init__(self): pass
    class ExpertQuantumMemoryEntry:
        def __init__(self): pass
    class MemoryPriority:
        HIGH = "high"
    class GamePhase:
        REGULAR = "regular"
    class MemoryDimension:
        BASKETBALL = "basketball"

# Configure expert logging
logging.basicConfig(
level=logging.INFO,
format=" %(asctime)s │ %(levelname)s │ %(name)s │ %(message)s",
datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("NBA_API_PARAMETER_MANAGER")

class BasketballLeague(Enum):
    """Basketball league identifiers"""
    NBA = "00"
    WNBA = "10"

class SeasonPhase(Enum):
    """Basketball season phases"""
    PRE_SEASON = "Pre Season"
    REGULAR_SEASON = "Regular Season"
    PLAYOFFS = "Playoffs"
    ALL_STAR = "All-Star"

class MeasureType(Enum):
    """NBA API measure types"""
    BASE = "Base"
    ADVANCED = "Advanced"
    MISC = "Misc"
    FOUR_FACTORS = "Four Factors"
    SCORING = "Scoring"
    OPPONENT = "Opponent"
    USAGE = "Usage"
    DEFENSE = "Defense"

class PerMode(Enum):
    """NBA API per mode options"""
    TOTALS = "Totals"
    PER_GAME = "PerGame"
    MINUTES_PER = "MinutesPer"
    PER_36 = "Per36"
    PER_100_POSSESSIONS = "Per100Possessions"
    PER_100_PLAYS = "Per100Plays"

class GameScope(Enum):
    """NBA API game scope options"""
    SEASON = "Season"
    LAST_10 = "Last 10"
    YESTERDAY = "Yesterday"
    FINALS = "Finals"

class PlayerExperience(Enum):
    """NBA API player experience options"""
    ROOKIE = "Rookie"
    SOPHOMORE = "Sophomore"
    VETERAN = "Veteran"

class PlayerPosition(Enum):
    """NBA API player position options"""
    F_C = "F-C"
    F_G = "F-G"
    C_F = "C-F"
    G_F = "G-F"
    FORWARD = "Forward"
    CENTER = "Center"
    GUARD = "Guard"

class StarterBench(Enum):
    """NBA API starter/bench options"""
    STARTERS = "Starters"
    BENCH = "Bench"

class DraftYear(Enum):
    """NBA API draft year ranges"""
    ALL = ""
    YEAR_2024 = "2024"
    YEAR_2023 = "2023"
    YEAR_2022 = "2022"
    YEAR_2021 = "2021"
    YEAR_2020 = "2020"

class Conference(Enum):
    """NBA API conference options"""
    EAST = "East"
    WEST = "West"

class Division(Enum):
    """NBA API division options"""
    ATLANTIC = "Atlantic"
    CENTRAL = "Central"
    SOUTHEAST = "Southeast"
    NORTHWEST = "Northwest"
    PACIFIC = "Pacific"
    SOUTHWEST = "Southwest"

@dataclass
class NBAEndpointParameters:
    """Complete NBA API endpoint parameter configuration"""

    # Core parameters
    league_id: str = BasketballLeague.NBA.value
    season: Optional[str] = None
    season_type: str = SeasonPhase.REGULAR_SEASON.value

    # Player/Team identifiers
    player_id: Optional[int] = None
    team_id: Optional[int] = None
    game_id: Optional[str] = None

    # Statistical parameters
    measure_type: str = MeasureType.BASE.value
    per_mode: str = PerMode.PER_GAME.value

    # Filtering parameters
    conference: Optional[str] = None
    division: Optional[str] = None
    game_scope: str = GameScope.SEASON.value
    player_experience: Optional[str] = None
    player_position: Optional[str] = None
    starter_bench: Optional[str] = None
    draft_year: Optional[str] = None

    # Date parameters
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    game_date: Optional[str] = None

    # Advanced parameters
    playoff_round: Optional[int] = None
    outcome: Optional[str] = None # 'W' or 'L'
    location: Optional[str] = None # 'Home' or 'Road'
    month: Optional[int] = None
    season_segment: Optional[str] = None # 'First Half', 'Second Half'
    period: Optional[int] = None
    vs_conference: Optional[str] = None
    vs_division: Optional[str] = None
    # Clutch parameters
    clutch_time: str = "Last 5 Minutes"
    ahead_behind: Optional[str] = None  # 'Ahead', 'Behind', 'Tied'
    point_diff: Optional[int] = 5

    # Tracking parameters
    distance_range: Optional[str] = None  # '5ft Range', '8ft Range', etc.
    general_range: Optional[str] = None  # 'Overall', 'Catch and Shoot', etc.
    dribble_range: Optional[str] = None  # '0 Dribbles', '1 Dribble', etc.
    shot_clock_range: Optional[str] = None  # '24-22', '22-18 Very Early', etc.
    touch_time_range: Optional[str] = None  # 'Touch < 2 Seconds', etc.

    # Pagination
    sort_field: Optional[str] = None
    sort_order: str = "ASC"

    def to_dict(self) -> Dict[str, Any]:
        """Convert parameters to dictionary for API calls"""
        params = {}
        for key, value in self.__dict__.items():
            if value is not None:
                # Convert enum values to strings
                if hasattr(value, 'value'):
                    params[key] = value.value
                else:
                    params[key] = value
        return params


class NBAParameterManager:
    """Advanced NBA API parameter management for MEDUSA integration"""

    def __init__(self, oracle_memory: ExpertOracleMemory):
        self.oracle_memory = oracle_memory
        self.historical_seasons = self._generate_historical_seasons()
        self.nba_teams = self._get_nba_teams()
        self.wnba_teams = self._get_wnba_teams()

        logger.info("NBA Parameter Manager initialized with Oracle Memory integration")

    def _generate_historical_seasons(self) -> List[str]:
        """Generate 10-year historical season list"""
        current_year = datetime.now().year
        seasons = []

        # NBA seasons (format: 2023-24)
        for year in range(current_year - 10, current_year + 1):
            nba_season = f"{year}-{str(year + 1)[-2:]}"
            seasons.append(nba_season)

        # WNBA seasons (format: 2024)
        for year in range(current_year - 10, current_year + 1):
            seasons.append(str(year))

        return seasons

    def _get_nba_teams(self) -> Dict[int, str]:
        """NBA team ID mappings"""
        return {
            1610612737: "Atlanta Hawks",
            1610612738: "Boston Celtics",
            1610612751: "Brooklyn Nets",
            1610612766: "Charlotte Hornets",
            1610612741: "Chicago Bulls",
            1610612739: "Cleveland Cavaliers",
            1610612742: "Dallas Mavericks",
            1610612743: "Denver Nuggets",
            1610612765: "Detroit Pistons",
            1610612744: "Golden State Warriors",
            1610612745: "Houston Rockets",
            1610612754: "Indiana Pacers",
            1610612746: "LA Clippers",
            1610612747: "Los Angeles Lakers",
            1610612763: "Memphis Grizzlies",
            1610612748: "Miami Heat",
            1610612749: "Milwaukee Bucks",
            1610612750: "Minnesota Timberwolves",
            1610612740: "New Orleans Pelicans",
            1610612752: "New York Knicks",
            1610612760: "Oklahoma City Thunder",
            1610612753: "Orlando Magic",
            1610612755: "Philadelphia 76ers",
            1610612756: "Phoenix Suns",
            1610612757: "Portland Trail Blazers",
            1610612758: "Sacramento Kings",
            1610612759: "San Antonio Spurs",
            1610612761: "Toronto Raptors",
            1610612762: "Utah Jazz",
            1610612764: "Washington Wizards"
        }

    def _get_wnba_teams(self) -> Dict[int, str]:
        """WNBA team ID mappings with current 2024 teams"""
        return {
            1611661312: "Atlanta Dream",
            1611661313: "Chicago Sky",
            1611661314: "Connecticut Sun",
            1611661315: "Dallas Wings",
            1611661316: "Indiana Fever",
            1611661317: "Las Vegas Aces",
            1611661318: "Minnesota Lynx",
            1611661319: "New York Liberty",
            1611661320: "Phoenix Mercury",
            1611661321: "Seattle Storm",
            1611661322: "Washington Mystics"
        }

    def get_team_name_by_id(self, team_id: int, league: str = "NBA") -> Optional[str]:
        """Get team name by ID for specified league"""
        if league.upper() == "WNBA":
            return self._get_wnba_teams().get(team_id)
        else:
            return self._get_nba_teams().get(team_id)

    def get_team_id_by_name(self, team_name: str, league: str = "NBA") -> Optional[int]:
        """Get team ID by name for specified league"""
        teams = self._get_wnba_teams() if league.upper() == "WNBA" else self._get_nba_teams()

        # Try exact match first
        for team_id, name in teams.items():
            if name.lower() == team_name.lower():
                return team_id

        # Try partial match
        for team_id, name in teams.items():
            if team_name.lower() in name.lower() or name.lower() in team_name.lower():
                return team_id

        return None

    def is_valid_team(self, team_name: str, league: str = "NBA") -> bool:
        """Check if team name is valid for the specified league"""
        return self.get_team_id_by_name(team_name, league) is not None

    async def create_league_dashboard_parameters(
        self,
        league: BasketballLeague,
        seasons: Optional[List[str]] = None,
        measure_types: Optional[List[MeasureType]] = None
    ) -> List[NBAEndpointParameters]:
        """Create comprehensive league dashboard parameter sets"""

        if seasons is None:
            seasons = self.historical_seasons[-3:]  # Last 3 seasons

        if measure_types is None:
            measure_types = [MeasureType.BASE, MeasureType.ADVANCED]

        parameter_sets = []

        for season in seasons:
            # Skip WNBA seasons in NBA format and vice versa
            if league == BasketballLeague.NBA and len(season) == 4:
                continue
            if league == BasketballLeague.WNBA and len(season) > 4:
                continue

            for measure_type in measure_types:
                for season_type in [SeasonPhase.REGULAR_SEASON, SeasonPhase.PLAYOFFS]:
                    params = NBAEndpointParameters(
                        league_id=league.value,
                        season=season,
                        season_type=season_type.value,
                        measure_type=measure_type.value,
                        per_mode=PerMode.PER_GAME.value
                    )
                    parameter_sets.append(params)

        logger.info(f"Created {len(parameter_sets)} league dashboard parameter sets for {league.name}")

        # Store in Oracle Memory
        await self._store_parameters_in_oracle_memory(
            parameter_sets,
            f"{league.name}_LEAGUE_DASHBOARD_PARAMS"
        )

        return parameter_sets

    async def create_player_dashboard_parameters(
        self,
        league: BasketballLeague,
        player_ids: Optional[List[int]] = None,
        seasons: Optional[List[str]] = None
    ) -> List[NBAEndpointParameters]:
        """Create comprehensive player dashboard parameter sets"""

        if seasons is None:
            seasons = self.historical_seasons[-2:]  # Last 2 seasons

        if player_ids is None:
            # Get top players from Oracle Memory or use defaults
            player_ids = await self._get_top_players_from_oracle(league)

        parameter_sets = []

        for player_id in player_ids:
            for season in seasons:
                # Skip incompatible season formats
                if league == BasketballLeague.NBA and len(season) == 4:
                    continue
                if league == BasketballLeague.WNBA and len(season) > 4:
                    continue

                for measure_type in [MeasureType.BASE, MeasureType.ADVANCED]:
                    params = NBAEndpointParameters(
                        league_id=league.value,
                        season=season,
                        season_type=SeasonPhase.REGULAR_SEASON.value,
                        player_id=player_id,
                        measure_type=measure_type.value,
                        per_mode=PerMode.PER_GAME.value
                    )
                    parameter_sets.append(params)

        logger.info(f"Created {len(parameter_sets)} player dashboard parameter sets for {league.name}")

        # Store in Oracle Memory
        await self._store_parameters_in_oracle_memory(
            parameter_sets,
            f"{league.name}_PLAYER_DASHBOARD_PARAMS"
        )

        return parameter_sets

    async def create_team_dashboard_parameters(
        self,
        league: BasketballLeague,
        seasons: Optional[List[str]] = None
    ) -> List[NBAEndpointParameters]:
        """Create comprehensive team dashboard parameter sets"""

        if seasons is None:
            seasons = self.historical_seasons[-3:]  # Last 3 seasons

        teams = self.nba_teams if league == BasketballLeague.NBA else self.wnba_teams
        parameter_sets = []

        for team_id in teams.keys():
            for season in seasons:
                # Skip incompatible season formats
                if league == BasketballLeague.NBA and len(season) == 4:
                    continue
                if league == BasketballLeague.WNBA and len(season) > 4:
                    continue

                for measure_type in [MeasureType.BASE, MeasureType.ADVANCED]:
                    params = NBAEndpointParameters(
                        league_id=league.value,
                        season=season,
                        season_type=SeasonPhase.REGULAR_SEASON.value,
                        team_id=team_id,
                        measure_type=measure_type.value,
                        per_mode=PerMode.PER_GAME.value
                    )
                    parameter_sets.append(params)

        logger.info(f"Created {len(parameter_sets)} team dashboard parameter sets for {league.name}")

        # Store in Oracle Memory
        await self._store_parameters_in_oracle_memory(
            parameter_sets,
            f"{league.name}_TEAM_DASHBOARD_PARAMS"
        )

        return parameter_sets

    async def create_game_data_parameters(
        self,
        league: BasketballLeague,
        seasons: Optional[List[str]] = None,
        game_ids: Optional[List[str]] = None
    ) -> List[NBAEndpointParameters]:
        """Create comprehensive game data parameter sets"""

        if seasons is None:
            seasons = self.historical_seasons[-1:]  # Last season

        if game_ids is None:
            # Get games from Oracle Memory or generate sample
            game_ids = await self._get_recent_games_from_oracle(league, seasons)

        parameter_sets = []

        for game_id in game_ids:
            # Box score parameters
            for endpoint_type in ['traditional', 'advanced', 'misc', 'scoring']:
                params = NBAEndpointParameters(
                    league_id=league.value,
                    game_id=game_id
                )
                parameter_sets.append(params)

        logger.info(f"Created {len(parameter_sets)} game data parameter sets for {league.name}")

        # Store in Oracle Memory
        await self._store_parameters_in_oracle_memory(
            parameter_sets,
            f"{league.name}_GAME_DATA_PARAMS"
        )

        return parameter_sets

    async def create_clutch_parameters(
        self,
        league: BasketballLeague,
        seasons: Optional[List[str]] = None
    ) -> List[NBAEndpointParameters]:
        """Create clutch situation parameter sets"""

        if seasons is None:
            seasons = self.historical_seasons[-2:]  # Last 2 seasons

        parameter_sets = []

        for season in seasons:
            # Skip incompatible season formats
            if league == BasketballLeague.NBA and len(season) == 4:
                continue
            if league == BasketballLeague.WNBA and len(season) > 4:
                continue

            for clutch_time in ["Last 5 Minutes", "Last 3 Minutes", "Last 1 Minute"]:
                for point_diff in [3, 5, 10]:
                    params = NBAEndpointParameters(
                        league_id=league.value,
                        season=season,
                        season_type=SeasonPhase.REGULAR_SEASON.value,
                        measure_type=MeasureType.BASE.value,
                        clutch_time=clutch_time,
                        point_diff=point_diff
                    )
                    parameter_sets.append(params)

        logger.info(f"Created {len(parameter_sets)} clutch parameter sets for {league.name}")

        # Store in Oracle Memory
        await self._store_parameters_in_oracle_memory(
            parameter_sets,
            f"{league.name}_CLUTCH_PARAMS"
        )

        return parameter_sets

    async def create_comprehensive_parameter_matrix(
        self,
        league: BasketballLeague,
        include_historical: bool = True
    ) -> Dict[str, List[NBAEndpointParameters]]:
        """Create comprehensive parameter matrix for all endpoints"""

        logger.info(f"Creating comprehensive parameter matrix for {league.name}")

        parameter_matrix = {}

        # League-wide parameters
        parameter_matrix['league_dashboard'] = await self.create_league_dashboard_parameters(league)

        # Team parameters
        parameter_matrix['team_dashboard'] = await self.create_team_dashboard_parameters(league)

        # Player parameters (subset for efficiency)
        top_players = await self._get_top_players_from_oracle(league, limit=50)
        parameter_matrix['player_dashboard'] = await self.create_player_dashboard_parameters(
            league, player_ids=top_players
        )

        # Game parameters
        parameter_matrix['game_data'] = await self.create_game_data_parameters(league)

        # Clutch parameters
        parameter_matrix['clutch_situations'] = await self.create_clutch_parameters(league)

        # Historical data parameters
        if include_historical:
            parameter_matrix['historical_comprehensive'] = await self._create_historical_parameters(league)

        total_params = sum(len(params) for params in parameter_matrix.values())
        logger.info(f"Created comprehensive parameter matrix with {total_params} total parameter sets")

        # Store complete matrix in Oracle Memory
        await self._store_parameter_matrix_in_oracle_memory(parameter_matrix, league)

        return parameter_matrix

    async def _get_top_players_from_oracle(
        self,
        league: BasketballLeague,
        limit: int = 100
    ) -> List[int]:
        """Get top players from Oracle Memory or return defaults"""

        try:
            # Try to retrieve from Oracle Memory
            memory_key = f"{league.name}_TOP_PLAYERS"
            recent_entries = self.oracle_memory.get_expert_temporal_context(lookback_hours=168, context_type="basketball")
            # Look for player data in recent entries
            for entry in recent_entries:
                if (entry.basketball_context and
                    entry.basketball_context.get('type') == 'TOP_PLAYERS' and
                    entry.basketball_context.get('league') == league.value):
                    player_data = entry.basketball_context.get('player_ids', [])
                    if player_data:
                        return player_data[:limit]

        except Exception as e:
            logger.warning(f"Could not retrieve top players from Oracle Memory: {e}")

        # Return default top players
        if league == BasketballLeague.NBA:
            return [
                2544,    # LeBron James
                201939,  # Stephen Curry
                201566,  # Russell Westbrook
                203507,  # Giannis Antetokounmpo
                1628369, # Luka Dončić
                203999,  # Nikola Jokić
                1629629, # Jayson Tatum
                201142,  # Kevin Durant
                203081,  # Damian Lillard
                1627732  # Ja Morant
            ][:limit]
        else:  # WNBA
            return [
                1628886, # A'ja Wilson
                1628887, # Breanna Stewart
                202154,  # Diana Taurasi
                1628888, # Sabrina Ionescu
                203924,  # Candace Parker
                1628889, # Kelsey Plum
                1628890, # Jewell Loyd
                1628891, # Arike Ogunbowale
                1628892, # Courtney Williams
                1628893  # Dearica Hamby
            ][:limit]
    async def _get_recent_games_from_oracle(
        self,
        league: BasketballLeague,
        seasons: List[str]
    ) -> List[str]:
        """Get recent game IDs from Oracle Memory"""

        try:
            # Try to retrieve from Oracle Memory
            memory_key = f"{league.name}_RECENT_GAMES"
            recent_entries = self.oracle_memory.get_expert_temporal_context(lookback_hours=24, context_type="basketball")
            # Look for game data in recent entries
            for entry in recent_entries:
                if (entry.basketball_context and
                    entry.basketball_context.get('type') == 'RECENT_GAMES' and
                    entry.basketball_context.get('league') == league.value):
                    game_data = entry.basketball_context.get('game_ids', [])
                    if game_data:
                        return game_data[:100]  # Limit for efficiency

        except Exception as e:
            logger.warning(f"Could not retrieve recent games from Oracle Memory: {e}")

        # Return sample game IDs
        if league == BasketballLeague.NBA:
            return [
                "0022400001", "0022400002", "0022400003", "0022400004", "0022400005",
                "0022400006", "0022400007", "0022400008", "0022400009", "0022400010"
            ]
        else:  # WNBA
            return [
                "1022400001", "1022400002", "1022400003", "1022400004", "1022400005",
                "1022400006", "1022400007", "1022400008", "1022400009", "1022400010"
            ]

    async def _create_historical_parameters(
        self,
        league: BasketballLeague
    ) -> List[NBAEndpointParameters]:
        """Create comprehensive historical data parameters"""

        historical_params = []
        historical_seasons = self.historical_seasons[-10:]  # Full 10 years

        for season in historical_seasons:
            # Skip incompatible season formats
            if league == BasketballLeague.NBA and len(season) == 4:
                continue
            if league == BasketballLeague.WNBA and len(season) > 4:
                continue

            # All-time leaders parameters
            params = NBAEndpointParameters(
                league_id=league.value,
                season_type=SeasonPhase.REGULAR_SEASON.value,
                per_mode=PerMode.TOTALS.value
            )
            historical_params.append(params)

        logger.info(f"Created {len(historical_params)} historical parameter sets")
        return historical_params

    async def _store_parameters_in_oracle_memory(
        self,
        parameters: List[NBAEndpointParameters],
        memory_key: str
    ) -> None:
        """Store parameter sets in Oracle Memory"""

        try:
            param_data = {
                'parameters': [param.to_dict() for param in parameters],
                'count': len(parameters),
                'created_at': datetime.now().isoformat(),
                'memory_key': memory_key
            }

            memory_entry = ExpertQuantumMemoryEntry(
                timestamp=datetime.now().isoformat(),
                event_type="NBA_PARAMETER_STORAGE",
                content=f"Stored {len(parameters)} NBA API parameters for {memory_key}",
                quantum_signature=f"nba_params_{memory_key}_{len(parameters)}",
                temporal_anchor=f"params_{memory_key}_{datetime.now().timestamp()}",
                tags=["nba_api", "parameters", memory_key],
                priority=MemoryPriority.IMPORTANT,
                game_phase=GamePhase.PREGAME,
                dimensions={MemoryDimension.BASKETBALL},
                basketball_context={
                    "parameter_count": len(parameters),
                    "memory_key": memory_key,
                    "basketball_aware": True
                }
            )

            self.oracle_memory.log_expert_event(
                event_type="NBA_PARAMETER_STORAGE",
                content=f"Stored {len(parameters)} NBA API parameters",
                tags=["nba_api", "parameters", memory_key],
                basketball_context=memory_entry.basketball_context,
                priority=MemoryPriority.IMPORTANT,
                game_phase=GamePhase.PREGAME
            )
            logger.info(f"Stored {len(parameters)} parameters in Oracle Memory: {memory_key}")

        except Exception as e:
            logger.error(f"Failed to store parameters in Oracle Memory: {e}")

    async def _store_parameter_matrix_in_oracle_memory(
        self,
        parameter_matrix: Dict[str, List[NBAEndpointParameters]],
        league: BasketballLeague
    ) -> None:
        """Store complete parameter matrix in Oracle Memory"""

        try:
            matrix_data = {}
            total_params = 0

            for category, params in parameter_matrix.items():
                matrix_data[category] = {
                    'parameters': [param.to_dict() for param in params],
                    'count': len(params)
                }
                total_params += len(params)

            matrix_entry = ExpertQuantumMemoryEntry(
                timestamp=datetime.now().isoformat(),
                event_type="NBA_PARAMETER_MATRIX_CREATION",
                content=f"Comprehensive NBA parameter matrix for {league.value}: {total_params} total parameters",
                quantum_signature=f"matrix_{league.name}_{total_params}",
                temporal_anchor=datetime.now().isoformat(),
                tags=["nba_api", "parameter_matrix", league.value.lower()],
                priority=MemoryPriority.CRITICAL,
                game_phase=GamePhase.PREGAME,
                dimensions={MemoryDimension.BASKETBALL, MemoryDimension.TEMPORAL},
                basketball_context={
                    'type': 'NBA_API_PARAMETER_MATRIX',
                    'league': league.name,
                    'total_parameters': total_params,
                    'basketball_aware': True,
                    'quantum_optimized': True
                }
            )

            self.oracle_memory.log_expert_event(
                event_type="NBA_PARAMETER_MATRIX_STORAGE",
                content=f"Stored comprehensive NBA parameter matrix for {league.value}",
                tags=["nba_api", "parameter_matrix", league.value.lower()],
                basketball_context=matrix_entry.basketball_context,
                priority=MemoryPriority.CRITICAL,
                game_phase=GamePhase.PREGAME
            )
            logger.info(f"Stored comprehensive parameter matrix in Oracle Memory: {total_params} total parameters")

        except Exception as e:
            logger.error(f"Failed to store parameter matrix in Oracle Memory: {e}")

# Example usage and testing
async def main():
    """Test NBA Parameter Manager"""
    # Initialize Oracle Memory with config
    oracle_config = {
        'memory_path': 'medusa_unified.db',
        'encryption_key': None,  # Will auto-generate
        'max_memory_size': 1000000,
        'basketball_analytics': True,
        'quantum_features': True
    }
    oracle_memory = ExpertOracleMemory(oracle_config)

    # Initialize Parameter Manager
    param_manager = NBAParameterManager(oracle_memory)

    # Create comprehensive parameter matrix for NBA
    nba_matrix = await param_manager.create_comprehensive_parameter_matrix(
        BasketballLeague.NBA,
        include_historical=True
    )

    # Create comprehensive parameter matrix for WNBA
    wnba_matrix = await param_manager.create_comprehensive_parameter_matrix(
        BasketballLeague.WNBA,
        include_historical=True
    )

    # Log summary
    nba_total = sum(len(params) for params in nba_matrix.values())
    wnba_total = sum(len(params) for params in wnba_matrix.values())

    logger.info(f"COMPREHENSIVE PARAMETER INTEGRATION COMPLETE")
    logger.info(f"NBA Parameter Sets: {nba_total}")
    logger.info(f"WNBA Parameter Sets: {wnba_total}")
    logger.info(f"Total Parameters: {nba_total + wnba_total}")
    logger.info(f"All parameters integrated into Oracle Memory for MEDUSA processing")


if __name__ == "__main__":
    asyncio.run(main())
